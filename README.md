# Ratziel

[![](https://www.codefactor.io/repository/github/theflooddragon/ratziel-beta/badge)](https://www.codefactor.io/repository/github/theflooddragon/ratziel-beta)
![](https://img.shields.io/github/languages/code-size/TheFloodDragon/Ratziel-Beta)

基于Taboolib开发的一个多功能插件。

## 功能

+ 物品引擎 (开发中)

## 版本

### Alpha-第一阶段 （当前阶段）

<h6>版本号：A.0.x.x.x</h6>

- [x] 基础内容完毕，有一些功能
- [x] 完善物品预加载，提升物品生成速率

### Beta-第二阶段

<h6>版本号：B.x.x.x.x</h6>

- [ ] 插件基础内容和部分扩展内容开发完成，开始测试

### RC-第三阶段

<h6>版本号：1.0.0-RC</h6>

- [ ] 第一个预发布版

第一个正式版出来后，随即进行正式发售哦！

# 购买

**作者QQ：** [1610105206](https://qm.qq.com/q/ZyeXCHare)

**售价：** ~~168R~~ 128R *（正式发售前优惠）*

*注：售价变动可能无法及时更新，具体价格以实际为准*

## 服务

| 服务项       | 免费版             | 付费版 |
| ------------ | ------------------ | ------ |
| **完整代码** | ⭕（落后于最新版）  | ✅      |
| 插件下载     | ⭕（自行构建）      | ✅      |
| 功能文档     | ✅                  | ✅      |
| **案例资源** | ⭕（社区提供）      | ✅      |
| **优先处理** | ❌                  | ✅      |
| 问题解答     | ⭕（Github Issues） | ✅      |
| 技术支持     | ❌                  | ✅      |
| 插件兼容     | ❌                  | ✅      |

## 构建

* [Gradle](https://gradle.org/) - 依赖关系管理

**Windows:**

    gradlew.bat clean build

**macOS/Linux:**

    ./gradlew clean build

构建工件在 `./outs` 文件夹中
