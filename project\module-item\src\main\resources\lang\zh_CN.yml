# 物品命令
Item-Give: '&8[&6R&8] &7成功给予玩家 &e{0} &7物品 &e{1} &a×{2} &7!'
Item-Give-All: '&8[&6R&8] &7成功给予所有玩家物品 &e{0} &a×{1} &7!'
Item-Get: '&8[&6R&8] &7你获得了物品 &e{0} &a×{1} &7!'

# NBT标签
NBTFormat-Entry-Key: # 0-浅层节点 1-目标位置 2-深层节点
  - type: json
    text: '[&6{0}]&7: '
    args:
      - suggest: '/nbt edit {1} {2} '
        hover: '点击编辑'
NBTFormat-Entry-Value: # 0-值 1-精确转换后的值
  - type: json
    text: '[&e{0}]'
    args:
      - copy: '{1}'
        hover: '点击复制'
NBTFormat-Retract: '  '
NBTFormat-Retract-List: '&f- '
NBTFormat-List-Empty: '&f[]'
NBTAction-Set: '&8[&6R&8] &7成功设置位于节点 &b{0} &7的NBT标签为: &d{1}' # 0-节点 1-值
NBTAction-Remove: '&8[&6R&8] &7成功删除位于节点 &b{0} &7的NBT标签' # 0-节点
NBTAction-EmptyTag: '&8[&6R&8] &c空标签'