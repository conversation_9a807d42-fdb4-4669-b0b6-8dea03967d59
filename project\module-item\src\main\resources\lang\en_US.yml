# Item Command
Item-Give: '&8[&6R&8] &7Successfully give player &e{0} &7item &e{1} &a×{2} &7!'
Item-Give-All: '&8[&6R&8] &7Successfully give all player item &e{0}&a×{1} &7!'
Item-Get: '&8[&6R&8] &7You have got item &e{0} &a×{1} &7just now!'

# NBT Tags
NBTFormat-Entry-Key:
  - type: json
    text: '[&6{0}]&7: '
    args:
      - suggest: '/nbt edit {1} {2} '
        hover: 'Click to edit'
NBTFormat-Entry-Value:
  - type: json
    text: '[&e{0}]'
    args:
      - copy: '{1}'
        hover: 'Click to copy'
NBTFormat-Retract: '  '
NBTFormat-Retract-List: '&f- '
NBTFormat-List-Empty: '&f[]'
NBTAction-Set: '&8[&6R&8] &7Successfully Set NBT in node &b{0} &7to: &d{1}'
NBTAction-Remove: '&8[&6R&8] &7Successfully Remove NBT in node &b{0}'
NBTAction-EmptyTag: '&8[&6R&8] &cEmpty NBT Tag'