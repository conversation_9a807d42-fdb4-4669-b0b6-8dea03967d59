# Language file - English

# Plugin Info
Plugin-Loading:
  - '&r'
  - '&7Loading &6Ratziel&7... &8{0}'
  - '&r'
Plugin-Enabled: '&8[&6Ratziel&r&8] &bINFO &8| &3Loading complete. Ratziel &av{0} &3now enabled, use freely.'
Plugin-Reloaded: '&8[&6R&8] &7Plugin has been reloaded &8({0}ms)'

# Workspace
Workspace-Initiated: '&8[&6R&8] &7Initialized &a{0} &7workspaces... &8({1}ms)'
Workspace-Loaded: '&8[&6R&8] &7Successfully evaluated &a{0} &7valid element... &8({1}ms)'
Element-File-Duplicated: '&8[&6R&8] &cFind duplicated element &7{0} &8(in file {1}), skipping...'
Element-File-Load-Failed: '&8[&6R&8] &cLoading &7{0} &cfailed.'
Element-File-Evaluate-Failed: '&8[&6R&8] &cEvaluating &7{0} (in file {1}) &cfailed.'
Element-File-Reload-Failed: '&8[&6R&8] &cAutomatically reloading &7{0} &cfailed.'
Element-File-Reload-Succeed: '&8[&6R&8] &7Automatically loaded the changes of &f{0} &7. &8({1}ms)'

# Element
Element-Header: '&8[&6R&8] &bINFO &8| &3Loaded Element:'
Element-Identifier-Format: '  &6Name &d{0}&6:'
Element-Info-Format:
  - '    &eType: &7{0}'
  - '    &eFile: &7{1}'
  - '    &eProperty: &7{2}'
Element-Type-Header: '&8[&6R&8] &bInfo &8| &3Registered ElementType:'
Element-Type-Namespace-Format: '  &6Space &d{0}&6:  &8(Name:Alias:Handlers)'
Element-Type-Info-Format: '    &7{0} | {1} | {2}'
