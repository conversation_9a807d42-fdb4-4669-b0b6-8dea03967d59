---
title: 前言
sidebar_position: 1
---

# Ratziel 介绍

Ratziel 是一款基于 Taboolib 开发的强大多功能 Minecraft 插件，旨在为服务器提供丰富的自定义物品和脚本功能。

## 主要功能

- **高级物品系统** - 创建具有自定义属性、行为和触发器的精美物品
- **多语言脚本引擎** - 支持 JavaScript、Kether、Jexl 等多种脚本语言
- **NBT 数据管理** - 完整的物品 NBT 数据操作和自定义功能
- **事件触发系统** - 通过物品触发各类事件执行自定义行为
- **插件兼容性** - 与 AzureFlow、NeigeItems 等物品插件良好兼容

## 技术特点

Ratziel 采用模块化设计，核心功能包括：

1. **物品模块** - 处理物品的创建、属性和行为
2. **脚本模块** - 提供多语言脚本执行环境
3. **兼容模块** - 实现与其他插件的无缝集成
4. **核心模块** - 提供基础API和工具类

## 购买

**作者QQ：** [1610105206](https://qm.qq.com/q/ZyeXCHare)

**售价：** ~~168R~~ 128R *（正式发售前优惠）*

*注：售价变动可能无法及时更新，具体价格以作者为准*

## 服务

| 服务项       | 免费版             | 付费版 |
| ------------ | ------------------ | ------ |
| **完整代码** | ⭕（落后于最新版）  | ✅      |
| 插件下载     | ⭕（自行构建）      | ✅      |
| 功能文档     | ✅                  | ✅      |
| **案例资源** | ⭕（社区提供）      | ✅      |
| **优先处理** | ❌                  | ✅      |
| 问题解答     | ⭕（Github Issues） | ✅      |
| 技术支持     | ❌                  | ✅      |
| 插件兼容     | ❌                  | ✅      |
