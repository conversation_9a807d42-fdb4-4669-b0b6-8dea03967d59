[{"value": "org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:$serialization", "transitive": false, "ignoreOptional": true, "ignoreException": false, "scopes": ["compile", "runtime"], "external": true}, {"value": "org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:$serialization", "transitive": false, "ignoreOptional": true, "ignoreException": false, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-api:$adventureApi", "test": ["net.kyori.adventure.text.Component"], "transitive": true, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-key:$adventureApi", "test": ["net.kyori.adventure.key.Key"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-text-serializer-gson:$adventureApi", "test": ["net.kyori.adventure.text.serializer.gson.GsonComponentSerializer"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-text-minimessage:$adventureApi", "test": ["net.kyori.adventure.text.minimessage.MiniMessage"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-text-serializer-legacy:$adventureApi", "test": ["net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-platform-api:$adventurePlatform", "test": ["net.kyori.adventure.platform.AudienceProvider"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-platform-bukkit:$adventurePlatform", "test": ["net.kyori.adventure.platform.bukkit.BukkitAudience"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-text-serializer-bungeecord:$adventurePlatform", "test": ["net.kyori.adventure.text.serializer.bungeecord.BungeeComponentSerializer"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-nbt:$adventureApi", "test": ["net.kyori.adventure.nbt.BinaryTag"], "transitive": true, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-platform-facet:$adventurePlatform", "test": ["net.kyori.adventure.platform.facet.FacetAudience"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-platform-viaversion:$adventurePlatform", "test": ["net.kyori.adventure.platform.viaversion.ViaFacet"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-text-serializer-json:$adventureApi", "test": ["net.kyori.adventure.text.serializer.json.JSONComponentSerializer"], "transitive": true, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-text-serializer-json-legacy-impl:$adventureApi", "test": ["net.kyori.adventure.text.serializer.json.legacyimpl.NBTLegacyHoverEventSerializer"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}, {"value": "net.kyori:adventure-text-serializer-gson-legacy-impl:$adventureApi", "test": ["net.kyori.adventure.text.serializer.gson.legacyimpl.NBTLegacyHoverEventSerializer"], "transitive": false, "ignoreOptional": true, "ignoreException": true, "scopes": ["compile", "runtime"], "external": true}]