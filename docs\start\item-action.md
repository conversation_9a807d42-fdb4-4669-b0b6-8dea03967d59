---
title: 物品动作
sidebar_position: 3
---

# 物品动作

:::info
有关物品交互的都需要 **物品动作** 来实现。
:::

## 触发器

:::info
**触发器** 是一个动作执行的必要条件。
当动作绑定的触发器被触发时，这个动作才会执行。
:::

## 动作配置

**节点名：** `actions`、`action`、`events`、`event`

按照惯例，先看例子:

```YAML
ExampleItem:
  item:
    ...
    actions:
      onAttack: # 物品触发器
        - 'player.sendMessage("你攻击了！")'
```

也是非常简单啊， `onAttack` 声明了一个 *触发器*，此触发器会在玩家手持该物品进行攻击时触发。

同时，下面的 `player.sendMessage("你攻击了！")` 被解析为 **动作语句块 (动作块)** ，并绑定上面的 `onAttack` 触发器。

而显然，动作块是一段代码，如果按照插件的默认设置的话，动作解释器将编译如上脚本为 JavaScript内容，此段 JavaScript 代码将在物品动作执行的时候被执行。

## 文档速查

物品触发器的完整列表参见 **[触发器](../quick-lookup/item-trigger.md)**。

至于 **动作块** 具体支持什么语法，请看 **[动作语句块](../quick-lookup/action-block.md)**。
