name: Plugin Build

on:
  workflow_dispatch:
  push:

env:
  GITHUB_REPO: ${{ github.repository }}
  GITHUB_USER: ${{ github.actor }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  build:
    if: contains(github.event.head_commit.message, '[skip]') == false
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: adopt
          java-version: 21
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
      - name: Make Gradle Wrapper Executable
        run: chmod +x ./gradlew
      - name: Build
        run: ./gradlew clean build
      - name: Capture Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: Artifacts
          path: outs/
      - name: Automatic Releases
        uses: slord399/action-automatic-releases@v1.0.1
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          automatic_release_tag: latest
          prerelease: false
          title: "自动构建最新版本"
          files: outs/*.*