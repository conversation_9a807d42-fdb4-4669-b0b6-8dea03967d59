# 语言文件——简体中文

# 插件信息
Plugin-Loading:
  - '&r'
  - '&7正在加载 &6Ratziel&7... &8{0}'
  #  - '    ____        __        _      __'
  #  - '   / __ \____ _/ /_____  (_)__  / /'
  #  - '  / /_/ / __ `/ __/_  / / / _ \/ / '
  #  - ' / _, _/ /_/ / /_  / /_/ /  __/ /  '
  #  - '/_/ |_|\__,_/\__/ /___/_/\___/_/   '
  - '&r'
Plugin-Enabled: '&8[&6Ratziel&8] &b信息 &8| &3加载完毕. Ratziel &av{0} &3现已启用, 敬请使用.'
Plugin-Reloaded: '&8[&6R&8] &7插件已重载完毕 &8({0}ms)'

# 工作空间
Workspace-Initiated: '&8[&6R&8] &7成功载入 &a{0} &7个工作空间... &8({1}ms)'
Workspace-Loaded: '&8[&6R&8] &7成功评估了 &a{0} &7个有效元素... &8({1}ms)'
Element-File-Duplicated: '&8[&6R&8] &c存在重名的元素 &7{0} &8({1}), 已跳过加载...'
Element-File-Load-Failed: '&8[&6R&8] &c加载文件 &7{0} &c失败.'
Element-File-Evaluate-Failed: '&8[&6R&8] &c评估元素 &7{0} ({1}) &c时失败.'
Element-File-Reload-Failed: '&8[&6R&8] &c自动重载 &7{0} &c失败.'
Element-File-Reload-Succeed: '&8[&6R&8] &7自动载入了 &f{0} &7的更改. &8({1}ms)'

# 元素
Element-Header: '&8[&6R&8] &b信息 &8| &3已加载元素列表:'
Element-Identifier-Format: '  &6名称 &d{0}&6:'
Element-Info-Format:
  - '    &e类型: &7{0}'
  - '    &e文件: &7{1}'
  - '    &e属性: &7{2}'
Element-Type-Header: '&8[&6R&8] &b信息 &8| &3元素类型列表:'
Element-Type-Namespace-Format: '  &6命名空间 &d{0}&6:  &8(名称:别名:处理器)'
Element-Type-Info-Format: '    &7{0} | {1} | {2}'
