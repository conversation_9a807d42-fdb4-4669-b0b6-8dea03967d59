name: Deploy to GitHub Pages

on:
  push:
    branches: [master]

env:
  BASE_URL: /Ratziel-Docs/

# 设置权限
permissions:
  contents: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      # 拉取代码
      - name: Checkout
        uses: actions/checkout@v4
      # 安装 Npm 并构建
      - name: Install and Build
        run: npm install && npm run build
      # 部署
      - name: Deploy
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          branch: gh-pages
          folder: build
